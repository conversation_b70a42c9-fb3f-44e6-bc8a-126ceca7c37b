import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/buyers/store_ai/buyer_store_ai_screen.dart';
import 'package:swadesic/features/seller/store_ai/store_ai_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_details_screen/store_details_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_links/store_links.dart';
import 'package:swadesic/features/common_buyer_seller_screen/contact_info/contact_info.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_bottom_sheet_screen/share_store_bottom_sheet_screen.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_settings/seller_store_settings_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_buttons/app_buttons.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/buyers/messaging/buffer/message_repository.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_valuation_card_overlay.dart';

class StoreDetailsScreen extends StatefulWidget {
  final StoreInfo storeInfo;
  const StoreDetailsScreen({
    Key? key,
    required this.storeInfo,
  }) : super(key: key);

  @override
  _StoreDetailsScreenState createState() => _StoreDetailsScreenState();
}

class _StoreDetailsScreenState extends State<StoreDetailsScreen>
    with AutomaticKeepAliveClientMixin<StoreDetailsScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //region Bloc
  late StoreDetailsBloc storeDetailsBloc;
  //endregion

  //region Init
  @override
  void initState() {
    storeDetailsBloc = StoreDetailsBloc(context, widget.storeInfo);
    storeDetailsBloc.init();
    super.initState();
  }
  //endregion

  //region Did update
  @override
  void didUpdateWidget(covariant StoreDetailsScreen oldWidget) {
    // storeDetailsBloc.init();
    super.didUpdateWidget(oldWidget);
  }

  //endregion

  //region Navigate to Store Valuation Card
  void _navigateToStoreValuationCard() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StoreValuationCardOverlay(
          storeReference: storeDetailsBloc.storeInfo.storeReference,
          storeInfo: storeDetailsBloc.storeInfo,
        );
      },
    );
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: body(),
    );
  }

  //region Body
  Widget body() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        ///Un-comment
        // widget.buyerViewStoreBloc.isStoreDetailExpand ? dropDownActive() : dropDownInActive(),
        // dropDownInActive(),
        // storeIconAndDetails(),
        storeIconAndDetailsApproach1(),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              aboutStore(),
              storeDesc(),
              storeType(),
              verticalSizedBox(3),
              storeUrl(),

              ///In-comment
              //followedBy(),
              storeOptions(),
            ],
          ),
        ),
      ],
    );
  }

  Widget storeIconAndDetailsApproach1() {
    return Container(
      color: Colors.white,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Base layout
          Column(
            children: [
              storeCoverImage(),
              const SizedBox(
                  height:
                      65), // Adjust for the height of profile and details block
              // Support and sales stats
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: StreamBuilder<bool>(
                  stream: storeDetailsBloc.refreshCountCtrl.stream,
                  builder: (context, snapshot) {
                    return Row(
                      children: [
                        InkWell(
                          onTap: () {
                            storeDetailsBloc.goToFollowerAndSupporters(
                              storeReference:
                                  storeDetailsBloc.storeInfo.storeReference!,
                            );
                          },
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text:
                                      "${storeDetailsBloc.storeInfo.supports!}",
                                  style: AppTextStyle.access0(
                                    textColor: AppColors.appBlack,
                                  ).copyWith(fontWeight: FontWeight.w600),
                                ),
                                TextSpan(
                                  text: CommonMethods.singularPluralText(
                                    item: storeDetailsBloc.storeInfo.supports!,
                                    isValueReturn: false,
                                    singular: AppStrings.supporter,
                                    plural: AppStrings.supporters,
                                  ),
                                  style: AppTextStyle.access0(
                                    textColor: AppColors.appBlack,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Text(" • ",
                            style: AppTextStyle.access0(
                              textColor: AppColors.appBlack,
                            )),
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: "${storeDetailsBloc.storeInfo.sales!}",
                                style: AppTextStyle.access0(
                                  textColor: AppColors.appBlack,
                                ).copyWith(fontWeight: FontWeight.w600),
                              ),
                              TextSpan(
                                text: CommonMethods.singularPluralText(
                                  item: storeDetailsBloc.storeInfo.sales!,
                                  isValueReturn: false,
                                  singular: AppStrings.sale,
                                  plural: AppStrings.sales,
                                ),
                                style: AppTextStyle.access0(
                                  textColor: AppColors.appBlack,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(" • ",
                            style: AppTextStyle.access0(
                              textColor: AppColors.appBlack,
                            )),
                        InkWell(
                          onTap: () {
                            storeDetailsBloc.goToStoreReviews(
                              storeInfo: storeDetailsBloc.storeInfo,
                            );
                          },
                          child: _buildReviewCountText(),
                        ),
                      ],
                    );
                  },
                ),
              ),
              const SizedBox(height: 6),
            ],
          ),

          // Overlapping profile + name/handle section
          Positioned(
            top: 150 - 18, // Adjust based on actual height of cover
            left: 10,
            right: 10,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                storeImageApproach1(),
                const SizedBox(width: 16),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 25),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                storeDetailsBloc.storeInfo.storeName ?? "",
                                style: AppTextStyle.usernameHeading(
                                  textColor: AppColors.appBlack,
                                ).copyWith(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  height: 1.2,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            Visibility(
                              visible:
                                  storeDetailsBloc.storeInfo.subscriptionType ==
                                      SubscriptionTypeEnum.PREMIUM.name,
                              child: VerifiedBadge(
                                margin: const EdgeInsets.only(left: 6),
                                height: 18,
                                width: 18,
                                subscriptionType:
                                    SubscriptionTypeEnum.PREMIUM.name,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          "@${storeDetailsBloc.storeInfo.storehandle ?? ""}",
                          style: AppTextStyle.access0(
                            textColor: AppColors.writingBlack1,
                          ).copyWith(fontSize: 14),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget storeImageApproach1() {
    return GestureDetector(
      onTap: () {
        CommonMethods.goToShareStoreAndProfile(
          context: context,
          storeInfo: storeDetailsBloc.storeInfo,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(35),
          border: Border.all(
            color: Colors.white,
            width: 3,
          ),
        ),
        child: CustomImageContainer(
          width: 74,
          height: 74,
          imageUrl: storeDetailsBloc.storeInfo.icon,
          imageType: CustomImageContainerType.store,
          showLevelBadge: true,
          level: storeDetailsBloc.storeInfo.storeLevel,
          badgeWidth: 25,
          badgeHeight: 25,
          onTapBadge: () {
            _navigateToStoreValuationCard();
          },
        ),
      ),
    );
  }

  // //endregion
  // Widget storeIconAndDetails() {
  //   return Container(
  //     color: Colors.white,
  //     child: Column(
  //       children: [
  //         // Cover image without padding - full width
  //         Stack(
  //           clipBehavior: Clip.none, // This is important to allow overflow
  //           children: [
  //             // Cover image from your method
  //             storeCoverImage(),
  //             // Profile + details overlapping the cover image with padding
  //             Positioned(
  //               bottom: -55,
  //               left: 16,
  //               right: 16,
  //               child: Row(
  //                 crossAxisAlignment: CrossAxisAlignment.end,
  //                 children: [
  //                   // Store profile image (wrap your method to apply styling)
  //                   storeImage(),
  //                   const SizedBox(width: 16),
  //                   // Store name and handle
  //                   Expanded(
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         const SizedBox(height: 20),
  //                         Row(
  //                           children: [
  //                             Expanded(
  //                               child: Text(
  //                                 storeDetailsBloc.storeInfo.storeName ?? "",
  //                                 style: AppTextStyle.usernameHeading(
  //                                   textColor: AppColors.appBlack,
  //                                 ).copyWith(
  //                                   fontWeight: FontWeight.bold,
  //                                   fontSize: 18,
  //                                   height: 1.2,
  //                                 ),
  //                                 overflow: TextOverflow.ellipsis,
  //                                 maxLines: 1,
  //                               ),
  //                             ),
  //                             Visibility(
  //                               visible: storeDetailsBloc
  //                                       .storeInfo.subscriptionType ==
  //                                   SubscriptionTypeEnum.PREMIUM.name,
  //                               child: VerifiedBadge(
  //                                 margin: const EdgeInsets.only(left: 6),
  //                                 height: 18,
  //                                 width: 18,
  //                                 subscriptionType:
  //                                     SubscriptionTypeEnum.PREMIUM.name,
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                         Text(
  //                           "@${storeDetailsBloc.storeInfo.storehandle ?? ""}",
  //                           style: AppTextStyle.contentHeading0(
  //                             textColor: AppColors.writingBlack1,
  //                           ).copyWith(fontSize: 14),
  //                           overflow: TextOverflow.ellipsis,
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //           ],
  //         ),
  //         const SizedBox(
  //             height:
  //                 70), // Increased to accommodate the overlapping profile image
  //         // Support and sales stats with padding
  //         Padding(
  //           padding: const EdgeInsets.symmetric(horizontal: 16),
  //           child: Padding(
  //             padding: const EdgeInsets.only(top: 10, left: 5),
  //             child: StreamBuilder<bool>(
  //               stream: storeDetailsBloc.refreshCountCtrl.stream,
  //               builder: (context, snapshot) {
  //                 return Row(
  //                   children: [
  //                     InkWell(
  //                       onTap: () {
  //                         storeDetailsBloc.goToFollowerAndSupporters(
  //                           storeReference:
  //                               storeDetailsBloc.storeInfo.storeReference!,
  //                         );
  //                       },
  //                       child: RichText(
  //                         text: TextSpan(
  //                           children: [
  //                             TextSpan(
  //                               text: "${storeDetailsBloc.storeInfo.supports!}",
  //                               style: AppTextStyle.access0(
  //                                 textColor: AppColors.appBlack,
  //                               ).copyWith(fontWeight: FontWeight.w600),
  //                             ),
  //                             TextSpan(
  //                               text: CommonMethods.singularPluralText(
  //                                 item: storeDetailsBloc.storeInfo.supports!,
  //                                 isValueReturn: false,
  //                                 singular: AppStrings.supporter,
  //                                 plural: AppStrings.supporters,
  //                               ),
  //                               style: AppTextStyle.access0(
  //                                 textColor: AppColors.appBlack,
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ),
  //                     Text(" • ",
  //                         style: AppTextStyle.access0(
  //                           textColor: AppColors.appBlack,
  //                         )),
  //                     RichText(
  //                       text: TextSpan(
  //                         children: [
  //                           TextSpan(
  //                             text: "${storeDetailsBloc.storeInfo.sales!}",
  //                             style: AppTextStyle.access0(
  //                               textColor: AppColors.appBlack,
  //                             ).copyWith(fontWeight: FontWeight.w600),
  //                           ),
  //                           TextSpan(
  //                             text: CommonMethods.singularPluralText(
  //                               item: storeDetailsBloc.storeInfo.sales!,
  //                               isValueReturn: false,
  //                               singular: AppStrings.sale,
  //                               plural: AppStrings.sales,
  //                             ),
  //                             style: AppTextStyle.access0(
  //                               textColor: AppColors.appBlack,
  //                             ),
  //                           ),
  //                         ],
  //                       ),
  //                     ),
  //                   ],
  //                 );
  //               },
  //             ),
  //           ),
  //         ),
  //         const SizedBox(height: 12),
  //       ],
  //     ),
  //   );
  // }

  //region Store Cover Image
  Widget storeCoverImage() {
    bool isOwner = AppConstants.appData.isStoreView! &&
        storeDetailsBloc.storeInfo.storeReference ==
            AppConstants.appData.storeReference;

    return InkWell(
      onTap: isOwner
          ? () {
              // Navigate to store profile editing screen
              storeDetailsBloc.onTapEditStore();
            }
          : null,
      child: Container(
        width: double.infinity,
        height: 150,
        child: Stack(
          children: [
            // Cover image or placeholder - stretched completely horizontally
            Container(
              width: double.infinity,
              height: 150,
              child: storeDetailsBloc.storeInfo.coverImage == null ||
                      storeDetailsBloc.storeInfo.coverImage!.isEmpty
                  ? Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [AppColors.borderColor1, AppColors.appWhite],
                        ),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.store,
                          size: 50,
                          color: AppColors.brandBlack,
                        ),
                      ),
                    )
                  : extendedImage(
                      storeDetailsBloc.storeInfo.coverImage!,
                      customPlaceHolder: AppImages.bannerPlaceHolder,
                      context,
                      400,
                      150,
                      fit: BoxFit.cover,
                    ),
            ),
            // Edit icon for store owners
            if (isOwner)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.edit,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  //endregion

  Widget _buildReviewCountText() {
    int totalReviews = storeDetailsBloc.storeInfo.storeReviewCount! +
        storeDetailsBloc.storeInfo.storeProductReviewCount!;

    // Check if current user is the store owner
    bool isStoreOwner = AppConstants.appData.isStoreView! &&
        storeDetailsBloc.storeInfo.storeReference ==
            AppConstants.appData.storeReference;

    // If no reviews
    if (totalReviews == 0) {
      return Text(
        isStoreOwner ? AppStrings.addAReview : AppStrings.noReviewsYet,
        style: AppTextStyle.access0(
          textColor: AppColors.appBlack,
        ),
      );
    }

    // If there are reviews, show count and text
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: "$totalReviews",
            style: AppTextStyle.access0(
              textColor: AppColors.appBlack,
            ).copyWith(fontWeight: FontWeight.w600),
          ),
          TextSpan(
            text: CommonMethods.singularPluralText(
              item: totalReviews,
              isValueReturn: false,
              singular: AppStrings.reviewLowerCase,
              plural: AppStrings.reviewsLowerCase,
            ),
            style: AppTextStyle.access0(
              textColor: AppColors.appBlack,
            ),
          ),
        ],
      ),
    );
  }

  Widget chatWithStoreAIButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: const Color(0xFFE7FFF1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          width: 2,
          color: AppColors.brandBlack,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (widget.storeInfo.storeAiMessagingUserId?.isEmpty ?? true) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Store AI chat is not available')),
              );
              return;
            }

            NewMessagingChatScreen.navigateToChat(
              context,
              connectingId: widget.storeInfo.storeAiMessagingUserId!,
              chatName: widget.storeInfo.storehandle?.isNotEmpty == true
                  ? widget.storeInfo.storehandle! + '_StoreAI'
                  : 'Store AI',
              chatIcon: widget.storeInfo.icon ?? '',
              entityType: 'STORE',
            );
          },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  AppImages.storeAIIcon,
                  width: 30,
                  height: 30,
                ),
                const SizedBox(width: 8),
                Text(
                  "Chat with ${widget.storeInfo.storeName ?? ''}'s StoreAI",
                  style: AppTextStyle.access0(
                    textColor: AppColors.appBlack,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  //endregion

// //region Store Image - Fixed version
//   Widget storeImage() {
//     return Container(
//       // Add explicit size to ensure proper hit testing
//       width: 80, // Slightly larger than the image to ensure full tap area
//       height: 80,
//       alignment: Alignment.center,
//       child: InkWell(
//         onTap: () {
//           CommonMethods.goToShareStoreAndProfile(
//             context: context,
//             storeInfo: storeDetailsBloc.storeInfo,
//           );
//         },
//         // Add explicit border radius to match the container
//         borderRadius: BorderRadius.circular(35),
//         child: Container(
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(35),
//             border: Border.all(
//               color: Colors.white,
//               width: 3,
//             ),
//           ),
//           child: CustomImageContainer(
//             width: 74,
//             height: 74,
//             imageUrl: storeDetailsBloc.storeInfo.icon,
//             imageType: CustomImageContainerType.store,
//             showLevelBadge: true,
//             level: storeDetailsBloc.storeInfo.storeLevel,
//             badgeWidth: 25,
//             badgeHeight: 25,
//           ),
//         ),
//       ),
//     );
//   }

  //region Store Name
  Widget storeName() {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            storeDetailsBloc.storeInfo.storeName ?? "",
            textAlign: TextAlign.center,
            style: AppTextStyle.usernameHeading(textColor: AppColors.appBlack)
                .copyWith(height: 0),
          ),
          Visibility(
            visible: storeDetailsBloc.storeInfo.subscriptionType ==
                SubscriptionTypeEnum.PREMIUM.name,
            child: VerifiedBadge(
              margin: const EdgeInsets.only(left: 5),
              height: 18,
              width: 18,
              subscriptionType: SubscriptionTypeEnum.PREMIUM.name,
            ),
            // child: Container(
            //     padding: const EdgeInsets.symmetric(horizontal: 10),
            //     child: SvgPicture.asset(AppImages.verified,height: 25,width: 25,)),
          )
        ],
      ),
    );
  }

//endregion

  //region Store Type
  Widget storeType() {
    return Container(
      width: double.infinity,
      child: Text(
        storeDetailsBloc.storeInfo.categoryName == null
            ? ""
            : storeDetailsBloc.storeInfo.categoryName!,
        textAlign: TextAlign.left,
        style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
      ),
    );
  }

  //endregion

  //region Support
  Widget support() {
    return StreamBuilder<bool>(
        stream: storeDetailsBloc.refreshCountCtrl.stream,
        builder: (context, snapshot) {
          return InkWell(
            onTap: () {
              storeDetailsBloc.goToFollowerAndSupporters(
                  storeReference: widget.storeInfo.storeReference!);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(storeDetailsBloc.storeInfo.supports!.toString(),
                    style: AppTextStyle.access0(textColor: AppColors.appBlack)),
                Text(
                  CommonMethods.singularPluralText(
                      item: storeDetailsBloc.storeInfo.supports!,
                      isValueReturn: false,
                      singular: AppStrings.supporter,
                      plural: AppStrings.supporters),
                  style: AppTextStyle.access0(textColor: AppColors.appBlack),
                ),
              ],
            ),
          );
        });
  }

  //endregion

  //region Sales
  Widget sales() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text("${storeDetailsBloc.storeInfo.sales!}",
            style: AppTextStyle.access0(textColor: AppColors.appBlack)),
        Text(
          CommonMethods.singularPluralText(
              item: storeDetailsBloc.storeInfo.sales!,
              isValueReturn: false,
              singular: AppStrings.sale,
              plural: AppStrings.sales),
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }

  //endregion

  //region Customers
  Widget customers() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          "550",
          style: TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.w600,
              fontFamily: 'LatoSemiBold',
              color: AppColors.appBlack),
        ),
        Text(
          AppStrings.customers,
          style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: 'LatoSemiBold',
              color: AppColors.appBlack),
        ),
      ],
    );
  }

  //endregion

  //region About store
  Widget aboutStore() {
    return StreamBuilder<bool>(
        stream: storeDetailsBloc.refreshCountCtrl.stream,
        builder: (context, snapshot) {
          return Container(
            alignment: Alignment.centerLeft,
            child: ReadMoreText(
              storeDetailsBloc.storeInfo.storeDesc!,
              trimMode: TrimMode.Line,
              trimLines: 3,
              colorClickableText: Colors.pink,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              lessStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
              moreStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
              trimLength: 5,
              trimCollapsedText: AppStrings.more,
              trimExpandedText: " ${AppStrings.less}",
              textAlign: TextAlign.start,
              annotations: [
                Annotation(
                  regExp: RegExp(r"@[a-zA-Z0-9_]+(?:'s\s+[a-zA-Z0-9\s]+)?"),
                  spanBuilder: ({required String text, TextStyle? textStyle}) =>
                      TextSpan(
                    text: text,
                    style: textStyle?.copyWith(color: AppColors.brandGreen),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // Extract the username from the tapped text
                        final userName = text.substring(1);
                        OnTapTag(context, userName);
                        //print(userName); // Print the username
                      },
                  ),
                ),
                //URL
                Annotation(
                  regExp: AppConstants.urlRegex,
                  spanBuilder: ({required String text, TextStyle? textStyle}) =>
                      TextSpan(
                    text: text,
                    style: textStyle?.copyWith(color: AppColors.brandBlack),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        CommonMethods.opeAppWebView(
                            webUrl: text,
                            context:
                                AppConstants.globalNavigator.currentContext!);
                        //print(text); // Print the URL
                      },
                  ),
                ),
              ],
            ),
            // child: GestureDetector(
            //   onTap: () {
            //     storeDetailsBloc.onTapStoreDesc(maxLine:storeDetailsBloc.maxStoreDescLine );
            //   },
            //   child: Padding(
            //     padding: const EdgeInsets.symmetric(horizontal: 10),
            //     child: RichText(
            //               textScaleFactor: MediaQuery.textScaleFactorOf(AppConstants.globalNavigator.currentContext!),
            //
            //       overflow: TextOverflow.ellipsis,
            //       text: TextSpan(
            //
            //           // style: DefaultTextStyle.of(context).style,
            //           children:
            //               CommonMethods.makeClickableText(context: context, sentence:storeDetailsBloc.storeInfo.storeDesc!)
            //        ),
            //       maxLines:storeDetailsBloc.maxStoreDescLine,
            //     ),
            //   ),
            // ),
          );
        });
  }

  //endregion

  //region Store desc
  Widget storeDesc() {
    return StreamBuilder<bool>(
        stream: storeDetailsBloc.refreshCountCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
            visible: storeDetailsBloc.maxStoreDescLine > 3,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              child: Row(
                children: [
                  Expanded(
                      child: StoreCommonWidgets.storeButtons(
                          textAndIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                AppStrings.showLessButtonText,
                                style: AppTextStyle.access0(
                                    textColor: AppColors.appBlack),
                              ),
                              horizontalSizedBox(10),
                              RotatedBox(
                                  quarterTurns: 2,
                                  child: SvgPicture.asset(AppImages.downArrow))
                            ],
                          ),
                          verticalPadding: 10,
                          onTapButton: () {
                            storeDetailsBloc.onTapStoreDesc(
                                maxLine: storeDetailsBloc.maxStoreDescLine);
                          })),
                ],
              ),
            ),
          );
        });
  }

  //endregion

  //region Store URL
  Widget storeUrl() {
    return Visibility(
      visible: storeDetailsBloc.storeInfo.storelinks!.isNotEmpty,
      child: Row(
        children: [
          InkWell(
            onTap: () async {
              //If link is not empty
              if (storeDetailsBloc.storeInfo.storelinks!.isNotEmpty) {
                await CommonMethods.appMinimumBottomSheets(
                    bottomSheetName: CommonMethods.singularPluralText(
                        item: storeDetailsBloc.storeInfo.storelinks!.length,
                        isValueReturn: false,
                        singular: AppStrings.link,
                        plural: AppStrings.links),
                    screen: StoreLinks(
                      storeLinks: storeDetailsBloc.storeInfo.storelinks!,
                      previousScreenContext: context,
                    ),
                    context: context);
              } else {
                //if no link are there
                CommonMethods.toastMessage(
                    AppStrings.thereAreNoLinksToShare, context);
              }
            },
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  AppImages.storeUrlIcon,
                  fit: BoxFit.cover,
                  height: 24,
                ),
                horizontalSizedBox(5),
                Text(
                    "${storeDetailsBloc.storeInfo.storelinks!.length} ${storeDetailsBloc.storeInfo.storelinks!.length <= 1 ? AppStrings.link : AppStrings.links}",
                    textAlign: TextAlign.left,
                    maxLines: 1,
                    style:
                        AppTextStyle.access0(textColor: AppColors.brandBlack)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Followed By
  Widget followedBy() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: Text("Supported by north_star129, viswaksen_y and 26 others ",
          textAlign: TextAlign.left,
          maxLines: 1,
          style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              fontFamily: 'LatoBold',
              color: AppColors.writingBlack)),
    );
  }

  //endregion

  //region Store options
  Widget storeOptions() {
    //Seller view own store
    if (AppConstants.appData.isStoreView! &&
        storeDetailsBloc.storeInfo.storeReference ==
            AppConstants.appData.storeReference) {
      return Consumer<StoreDashboardDataModel>(
        builder: (BuildContext context, StoreDashboardDataModel data,
            Widget? child) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //Share store
                Expanded(
                    child: StoreCommonWidgets.storeButtons(
                        textAndIcon: Text(
                          AppStrings.shareStore,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.access0(
                              textColor: AppColors.appWhite),
                        ),
                        verticalPadding: 10,
                        buttonColor: AppColors.brandBlack,
                        onTapButton: () async {
                          //If store is live then open share
                          if (data.storeDashBoard.isActive!) {
                            await CommonMethods.appMinimumBottomSheets(
                              screen: ShareInSocialBottomSheetScreen(
                                  storeInfo: storeDetailsBloc.storeInfo),
                              context: context,
                            );
                          }
                          //else Show message
                          else {
                            return CommonMethods.toastMessage(
                                AppStrings.beforeStoreActivationStoreLink,
                                context);
                          }
                        })),
                horizontalSizedBox(10),
                //Edit store
                Expanded(
                    child: StoreCommonWidgets.storeButtons(
                        textAndIcon: Text(
                          AppStrings.editStore,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                        verticalPadding: 10,
                        onTapButton: () {
                          storeDetailsBloc.onTapEditStore();
                        })),
                horizontalSizedBox(10),
                //Settings
                Expanded(
                    child: StoreCommonWidgets.storeButtons(
                        textAndIcon: Text(
                          AppStrings.settings,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                        verticalPadding: 10,
                        onTapButton: () {
                          var screen = SellerStoreSettingsScreen(
                            storeRef:
                                storeDetailsBloc.storeInfo.storeReference!,
                            storeId: storeDetailsBloc.storeInfo.storeid!,
                          );
                          var route =
                              MaterialPageRoute(builder: (context) => screen);
                          Navigator.push(context, route);
                        })),
              ],
            ),
          );
        },
      );
    }
    //Customer view
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          //Support Store
          // Expanded(
          //     child:storeDetailsBloc.storeInfo.followStatus!.toLowerCase()==FollowEnum.SUPPORT.name.toLowerCase()
          //         ?
          //
          //     StoreCommonWidgets.storeButtons(
          //         textAndIcon: Text(
          //           storeDetailsBloc.storeInfo.followStatus!,
          //           textAlign: TextAlign.center,
          //           overflow: TextOverflow.ellipsis,
          //
          //           style: AppTextStyle.access0(textColor: AppColors.appWhite),
          //         ),
          //         verticalPadding: 10,
          //         buttonColor: AppColors.brandGreen,
          //         onTapButton: () {
          //           storeDetailsBloc.onTapFollowAndSupport();
          //         })
          //
          //
          //     :
          //     StoreCommonWidgets.storeButtons(
          //         textAndIcon: Text(
          //           storeDetailsBloc.storeInfo.followStatus!,
          //           overflow: TextOverflow.ellipsis,
          //           textAlign: TextAlign.center,
          //           style: AppTextStyle.access0(textColor: AppColors.appBlack),
          //         ),
          //         verticalPadding: 10,
          //         onTapButton: () {
          //           storeDetailsBloc.onTapFollowAndSupport();
          //         })
          //
          //         ),
          ///Support
          Expanded(
            child: StreamBuilder<AppState>(
                stream: storeDetailsBloc.followUnFollowStateCtrl.stream,
                initialData: AppState.Success,
                builder: (context, snapshot) {
                  //Success
                  if (snapshot.data == AppState.Success) {
                    return storeDetailsBloc.storeInfo.followStatus!
                                    .toLowerCase() ==
                                FollowEnum.SUPPORT.name.toLowerCase() ||
                            storeDetailsBloc.storeInfo.followStatus!
                                    .toLowerCase() ==
                                "support back"
                        ? StoreCommonWidgets.storeButtons(
                            textAndIcon: Text(
                              storeDetailsBloc.storeInfo.followStatus!,
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyle.access0(
                                  textColor: AppColors.appWhite),
                            ),
                            verticalPadding: 10,
                            horizontalPadding: 10,
                            buttonColor: AppColors.brandBlack,
                            onTapButton: () {
                              storeDetailsBloc.onTapFollowAndSupport();
                            })
                        : StoreCommonWidgets.storeButtons(
                            textAndIcon: Text(
                              storeDetailsBloc.storeInfo.followStatus!,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                              style: AppTextStyle.access0(
                                  textColor: AppColors.appBlack),
                            ),
                            verticalPadding: 10,
                            onTapButton: () {
                              storeDetailsBloc.onTapFollowAndSupport();
                            });
                  }
                  //Loading
                  if (snapshot.data == AppState.Loading) {
                    return StoreCommonWidgets.storeButtons(
                        textAndIcon: Text(
                          AppStrings.loading,
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                        verticalPadding: 10,
                        onTapButton: () {});
                  }
                  return StoreCommonWidgets.storeButtons(
                      textAndIcon: const Icon(Icons.warning,
                          color: AppColors.yellow, size: 20),
                      verticalPadding: 10,
                      horizontalPadding: 40,
                      onTapButton: () {});
                }),
          ),

          horizontalSizedBox(10),

          ///Contact
          Expanded(
            child: StoreCommonWidgets.storeButtons(
              textAndIcon: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Flexible(
                    child: InkWell(
                      onTap: () {
                        //Check if static user then open login screen
                        if (CommonMethods().isStaticUser()) {
                          CommonMethods().goToSignUpFlow();
                          return;
                        }
                        NewMessagingChatScreen.navigateToChat(
                          context,
                          connectingId: widget.storeInfo.newMessagingUserId!,
                          chatName: widget.storeInfo.storehandle!,
                          chatIcon: widget.storeInfo.icon ?? '',
                          entityType: 'STORE',
                        );
                      },
                      child: Text(
                        "Message",
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                        style:
                            AppTextStyle.access0(textColor: AppColors.appBlack),
                      ),
                    ),
                  ),
                  horizontalSizedBox(5),
                  InkWell(
                    onTap: () async {
                      showModalBottomSheet(
                        context: context,
                        builder: (context) => ChatOptionsBottomSheet(
                          storeInfo: widget.storeInfo,
                          storeDetails: widget.storeInfo.storeDetails!.first,
                        ),
                      );
                    },
                    child: SizedBox(
                      height: 20,
                      width: 20,
                      child: SvgPicture.asset(AppImages.arrow),
                    ),
                  )
                ],
              ),
              verticalPadding: 10,
              horizontalPadding: 5,
              leftOffset: 10,
            ),
          ),

          horizontalSizedBox(10),

          ///Trust Center Icon
          Container(
            width: 42,
            height: 42,
            decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(37),
            ),
            child: InkWell(
              onTap: () {
                storeDetailsBloc.goToTrustCenter(
                    storeReference: storeDetailsBloc.storeInfo.storeReference!);
              },
              borderRadius: BorderRadius.circular(24),
              child: Center(
                child: SvgPicture.asset(
                  AppImages.trustCenterIcon,
                  width: 24,
                  height: 24,
                  fit: BoxFit.contain,
                  color: AppColors.brandBlack,
                ),
              ),
            ),
          ),

          horizontalSizedBox(10),

          ///Share Icon
          Container(
            width: 42,
            height: 42,
            decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(37),
            ),
            child: InkWell(
              onTap: () async {
                await CommonMethods.appMinimumBottomSheets(
                  screen: ShareInSocialBottomSheetScreen(
                      storeInfo: storeDetailsBloc.storeInfo),
                  context: context,
                );
              },
              borderRadius: BorderRadius.circular(24),
              child: Center(
                child: SvgPicture.asset(
                  AppImages.share,
                  width: 24,
                  height: 24,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
//endregion
}

class ChatOptionsBottomSheet extends StatelessWidget {
  final StoreInfo storeInfo;
  final StoreDetails storeDetails;

  const ChatOptionsBottomSheet({
    Key? key,
    required this.storeInfo,
    required this.storeDetails,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Chat Options',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          // _buildOptionTile(
          //   icon: Icons.refresh,
          //   title: 'Talk to sassy_designs-storeAI',
          //   isRecommended: true,
          //   onTap: () {
          //     if (storeInfo.storeAiMessagingUserId?.isEmpty ?? true) {
          //       ScaffoldMessenger.of(context).showSnackBar(
          //         const SnackBar(
          //             content: Text('Store AI chat is not available')),
          //       );
          //       return;
          //     }

          //     NewMessagingChatScreen.navigateToChat(
          //       context,
          //       connectingId: storeInfo.storeAiMessagingUserId!,
          //       chatName: storeInfo.storehandle?.isNotEmpty == true
          //           ? storeInfo.storehandle! + '_StoreAI'
          //           : 'Store AI',
          //       chatIcon: storeInfo.icon ?? '',
          //       entityType: 'STORE',
          //     );
          //   },
          // ),
          _buildOptionTile(
            icon: Icons.chat_bubble_outline,
            title: 'Send a Message on Swadesic',
            onTap: () {
              //Check if static user then open login screen
              if (CommonMethods().isStaticUser()) {
                CommonMethods().goToSignUpFlow();
                return;
              }
              NewMessagingChatScreen.navigateToChat(
                context,
                connectingId: storeInfo.newMessagingUserId!,
                chatName: storeInfo.storehandle!,
                chatIcon: storeInfo.icon ?? '',
                entityType: 'STORE',
              );
            },
          ),
          _buildOptionTile(
            icon: Icons.whatshot,
            title: 'Send a WhatsApp Message',
            onTap: () async {
              Navigator.pop(context);
              if (storeDetails.phoneNumber?.isNotEmpty == true) {
                CommonMethods.messageOnWhatsApp(
                    phoneNumber: storeDetails.phoneNumber!.first);
              }
            },
          ),
          _buildOptionTile(
            icon: Icons.email_outlined,
            title: 'Send an Email',
            onTap: () {
              Navigator.pop(context);
              if (storeDetails.email?.isNotEmpty == true) {
                CommonMethods.openEmail(emailId: storeDetails.email!.first);
              }
            },
          ),
          _buildOptionTile(
            icon: Icons.phone_outlined,
            title: 'Call the Store Owner',
            onTap: () {
              Navigator.pop(context);
              if (storeDetails.phoneNumber?.isNotEmpty == true) {
                CommonMethods.openDialPad(
                    phoneNumber: storeDetails.phoneNumber!.first);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    bool isRecommended = false,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (isRecommended)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Recommended',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
